{"name": "@deepgram/captions", "version": "1.2.0", "description": "Node implementation of Deepgram's WebVTT and SRT formatting. Given a transcription, this package can return a valid string to store as WebVTT or SRT caption files.", "keywords": ["audio", "javascript", "youtube", "typescript", "sdk", "ffmpeg", "speech", "subtitles", "srt", "webvtt", "speech-to-text", "transcription", "stt", "asr", "closed-captions", "deepgram"], "homepage": "https://github.com/deepgram/deepgram-node-captions", "bugs": "https://github.com/deepgram/deepgram-node-captions/issues", "license": "MIT", "author": {"name": "Deepgram DevRel Team", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>> (https://lukeocodes.dev)"], "files": ["dist", "src"], "engines": {"node": ">=18.0.0"}, "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "sideEffects": false, "repository": "deepgram/deepgram-node-captions", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "build:umd": "webpack --mode=production", "watch": "nodemon -e ts --watch src --exec \"npm run build\"", "test": "mocha -r ts-node/register test/*test.ts test/**/*test.ts --insect --timeout 5000 || :", "test:coverage": "nyc --reporter=lcovonly --reporter=text --reporter=text-summary npm run test"}, "dependencies": {"dayjs": "^1.11.10"}, "devDependencies": {"@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^17.6.7", "@types/chai": "^4.3.5", "@types/mocha": "^9.1.1", "chai": "^4.3.7", "husky": "^4.3.0", "mocha": "^9.2.2", "nodemon": "^3.0.1", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "prettier": "^2.5.1", "pretty-quick": "^3.1.3", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "srt-validator": "^6.0.4", "ts-loader": "^8.0.11", "ts-node": "^10.9.1", "typescript": "^4.5.5", "webpack-cli": "^4.9.2", "webpack": "^5.69.1"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "jsdelivr": "dist/umd/deepgram-captions.js", "unpkg": "dist/umd/deepgram-captions.js"}
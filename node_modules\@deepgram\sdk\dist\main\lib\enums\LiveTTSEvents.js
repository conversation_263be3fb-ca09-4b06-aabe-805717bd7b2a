"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LiveTTSEvents = void 0;
/**
 * Enumeration of events related to live text-to-speech synthesis.
 *
 * - `Open`: Built-in socket event for when the connection is opened.
 * - `Close`: Built-in socket event for when the connection is closed.
 * - `Error`: Built-in socket event for when an error occurs.
 * - `Metadata`: Event for when metadata is received.
 * - `Flushed`: Event for when the server has flushed the buffer.
 * - `Warning`: Event for when a warning is received.
 * - `Unhandled`: Catch-all event for any other message event.
 */
var LiveTTSEvents;
(function (LiveTTSEvents) {
    /**
     * Built in socket events.
     */
    LiveTTSEvents["Open"] = "Open";
    LiveTTSEvents["Close"] = "Close";
    LiveTTSEvents["Error"] = "Error";
    /**
     * Message { type: string }
     */
    LiveTTSEvents["Metadata"] = "Metadata";
    LiveTTSEvents["Flushed"] = "Flushed";
    LiveTTSEvents["Warning"] = "Warning";
    /**
     * Audio data event.
     */
    LiveTTSEvents["Audio"] = "Audio";
    /**
     * Catch all for any other message event
     */
    LiveTTSEvents["Unhandled"] = "Unhandled";
})(LiveTTSEvents = exports.LiveTTSEvents || (exports.LiveTTSEvents = {}));
//# sourceMappingURL=LiveTTSEvents.js.map
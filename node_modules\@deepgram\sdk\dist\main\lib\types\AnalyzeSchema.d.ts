/**
 * Options for read analysis
 */
interface AnalyzeSchema extends Record<string, unknown> {
    /**
     * @see https://developers.deepgram.com/docs/callback
     */
    callback?: string;
    /**
     * @see https://developers.deepgram.com/docs/callback#results
     */
    callback_method?: "put" | "post";
    custom_intent?: string | string[];
    custom_intent_mode?: "strict" | "extended";
    custom_topic?: string | string[];
    custom_topic_mode?: "strict" | "extended";
    intents?: boolean;
    language?: string;
    summarize?: boolean;
    sentiment?: boolean;
    topics?: boolean;
    /**
     * @see https://developers.deepgram.com/docs/extra-metadata
     */
    extra?: string[] | string;
}
export type { AnalyzeSchema };
//# sourceMappingURL=AnalyzeSchema.d.ts.map
export interface ListOnPremCredentialsResponse {
    distribution_credentials: OnPremCredentialResponse[];
}
export interface OnPremCredentialResponse {
    member: {
        member_id: string;
        email: string;
    };
    distribution_credentials: {
        distribution_credentials_id: string;
        provider: string;
        comment: string;
        scopes: string[];
        created: string;
    };
}
//# sourceMappingURL=ListOnPremCredentialsResponse.d.ts.map
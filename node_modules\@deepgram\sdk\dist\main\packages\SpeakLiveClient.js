"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpeakLiveClient = void 0;
const AbstractLiveClient_1 = require("./AbstractLiveClient");
const enums_1 = require("../lib/enums");
/**
 * The `SpeakLiveClient` class extends the `AbstractLiveClient` class and provides functionality for setting up and managing a WebSocket connection for live text-to-speech synthesis.
 *
 * The constructor takes in `DeepgramClientOptions` and an optional `SpeakSchema` object, as well as an optional `endpoint` string. It then calls the `connect` method of the parent `AbstractLiveClient` class to establish the WebSocket connection.
 *
 * The `setupConnection` method is responsible for handling the various events that can occur on the WebSocket connection, such as opening, closing, and receiving messages. It sets up event handlers for these events and emits the appropriate events based on the message type.
 *
 * The `configure` method allows you to send additional configuration options to the connected session.
 *
 * The `requestClose` method requests the server to close the connection.
 */
class SpeakLiveClient extends AbstractLiveClient_1.AbstractLiveClient {
    /**
     * Constructs a new `SpeakLiveClient` instance with the provided options.
     *
     * @param options - The `DeepgramClientOptions` to use for the client connection.
     * @param speakOptions - An optional `SpeakSchema` object containing additional configuration options for the text-to-speech.
     * @param endpoint - An optional string representing the WebSocket endpoint to connect to. Defaults to `:version/speak`.
     */
    constructor(options, speakOptions = {}, endpoint = ":version/speak") {
        super(options);
        this.namespace = "speak";
        this.connect(speakOptions, endpoint);
    }
    /**
     * Sets up the connection event handlers.
     * This method is responsible for handling the various events that can occur on the WebSocket connection, such as opening, closing, and receiving data.
     * - When the connection is opened, it emits the `LiveTTSEvents.Open` event.
     * - When the connection is closed, it emits the `LiveTTSEvents.Close` event.
     * - When an error occurs on the connection, it emits the `LiveTTSEvents.Error` event.
     * - When a message is received, it parses the message and emits the appropriate event based on the message type, such as `LiveTTSEvents.Metadata`, `LiveTTSEvents.Flushed`, and `LiveTTSEvents.Warning`.
     */
    setupConnection() {
        // Set up standard connection events (open, close, error) using abstracted method
        this.setupConnectionEvents({
            Open: enums_1.LiveTTSEvents.Open,
            Close: enums_1.LiveTTSEvents.Close,
            Error: enums_1.LiveTTSEvents.Error,
        });
        // Set up message handling specific to text-to-speech
        if (this.conn) {
            this.conn.onmessage = (event) => {
                this.handleMessage(event);
            };
        }
    }
    /**
     * Handles text messages received from the WebSocket connection.
     * @param data - The parsed JSON data.
     */
    handleTextMessage(data) {
        if (data.type === enums_1.LiveTTSEvents.Metadata) {
            this.emit(enums_1.LiveTTSEvents.Metadata, data);
        }
        else if (data.type === enums_1.LiveTTSEvents.Flushed) {
            this.emit(enums_1.LiveTTSEvents.Flushed, data);
        }
        else if (data.type === enums_1.LiveTTSEvents.Warning) {
            this.emit(enums_1.LiveTTSEvents.Warning, data);
        }
        else {
            this.emit(enums_1.LiveTTSEvents.Unhandled, data);
        }
    }
    /**
     * Handles binary messages received from the WebSocket connection.
     * @param data - The binary data.
     */
    handleBinaryMessage(data) {
        this.emit(enums_1.LiveTTSEvents.Audio, data);
    }
    /**
     * Sends a text input message to the server.
     *
     * @param {string} text - The text to convert to speech.
     */
    sendText(text) {
        this.send(JSON.stringify({
            type: "Speak",
            text,
        }));
    }
    /**
     * Requests the server flush the current buffer and return generated audio.
     */
    flush() {
        this.send(JSON.stringify({
            type: "Flush",
        }));
    }
    /**
     * Requests the server clear the current buffer.
     */
    clear() {
        this.send(JSON.stringify({
            type: "Clear",
        }));
    }
    /**
     * Requests the server close the connection.
     */
    requestClose() {
        this.send(JSON.stringify({
            type: "Close",
        }));
    }
    /**
     * Handles incoming messages from the WebSocket connection.
     * @param event - The MessageEvent object representing the received message.
     */
    handleMessage(event) {
        var _a, _b, _c, _d, _e, _f;
        if (typeof event.data === "string") {
            try {
                const data = JSON.parse(event.data);
                this.handleTextMessage(data);
            }
            catch (error) {
                this.emit(enums_1.LiveTTSEvents.Error, {
                    event,
                    message: "Unable to parse `data` as JSON.",
                    error,
                    url: (_a = this.conn) === null || _a === void 0 ? void 0 : _a.url,
                    readyState: (_b = this.conn) === null || _b === void 0 ? void 0 : _b.readyState,
                    data: ((_c = event.data) === null || _c === void 0 ? void 0 : _c.toString().substring(0, 200)) +
                        (((_d = event.data) === null || _d === void 0 ? void 0 : _d.toString().length) > 200 ? "..." : ""),
                });
            }
        }
        else if (event.data instanceof Blob) {
            event.data.arrayBuffer().then((buffer) => {
                this.handleBinaryMessage(Buffer.from(buffer));
            });
        }
        else if (event.data instanceof ArrayBuffer) {
            this.handleBinaryMessage(Buffer.from(event.data));
        }
        else if (Buffer.isBuffer(event.data)) {
            this.handleBinaryMessage(event.data);
        }
        else {
            console.log("Received unknown data type", event.data);
            this.emit(enums_1.LiveTTSEvents.Error, {
                event,
                message: "Received unknown data type.",
                url: (_e = this.conn) === null || _e === void 0 ? void 0 : _e.url,
                readyState: (_f = this.conn) === null || _f === void 0 ? void 0 : _f.readyState,
                dataType: typeof event.data,
            });
        }
    }
}
exports.SpeakLiveClient = SpeakLiveClient;
//# sourceMappingURL=SpeakLiveClient.js.map